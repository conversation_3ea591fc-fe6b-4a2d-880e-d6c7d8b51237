<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>NumJS Loading Test</title>
</head>
<body>
    <h1>NumJS Loading Test</h1>
    <div id="status">Loading...</div>
    <div id="test-results"></div>
    
    <script>
        // Test function to check if NumJS is loaded and working
        function testNumJS() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('test-results');
            
            console.log("Testing NumJS availability...");
            
            if (typeof nj === 'undefined') {
                statusDiv.innerHTML = 'NumJS is not loaded!';
                statusDiv.style.color = 'red';
                console.error("NumJS (nj) is not defined");
                return false;
            }
            
            try {
                // Test basic NumJS functionality
                const testArray = nj.ones([3, 3]);
                console.log("NumJS test array created:", testArray);
                
                const multiplied = testArray.multiply(255);
                console.log("NumJS multiplication test:", multiplied);
                
                statusDiv.innerHTML = 'NumJS loaded successfully!';
                statusDiv.style.color = 'green';
                
                resultsDiv.innerHTML = `
                    <h3>Test Results:</h3>
                    <p>✓ nj.ones([3,3]) works</p>
                    <p>✓ Array multiplication works</p>
                    <p>✓ NumJS version: ${nj.version || 'unknown'}</p>
                `;
                
                return true;
            } catch (e) {
                statusDiv.innerHTML = 'NumJS loaded but has errors!';
                statusDiv.style.color = 'orange';
                resultsDiv.innerHTML = `<p>Error: ${e.message}</p>`;
                console.error("NumJS test error:", e);
                return false;
            }
        }
        
        // Test immediately
        testNumJS();
        
        // Also test after a delay to simulate async loading
        setTimeout(testNumJS, 1000);
        setTimeout(testNumJS, 2000);
    </script>
    
    <!-- Load NumJS -->
    <script src="https://cdn.jsdelivr.net/gh/nicolaspanel/numjs@0.15.1/dist/numjs.min.js"></script>
    
    <script>
        // Test again after NumJS is loaded
        setTimeout(testNumJS, 100);
    </script>
</body>
</html>
